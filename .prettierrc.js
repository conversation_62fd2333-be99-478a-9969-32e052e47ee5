const { NODE_VIRTUAL_ENV } = process.env;

// When running in pre-commit hook, the plugin is installed globally.
// So we can't just use the package name, we need to use the full path.
const pluginTailwindcss = NODE_VIRTUAL_ENV
  ? `${NODE_VIRTUAL_ENV}/lib/node_modules/prettier-plugin-tailwindcss/dist/index.mjs`
  : "prettier-plugin-tailwindcss";
const pluginOrganizeImports = NODE_VIRTUAL_ENV
  ? `${NODE_VIRTUAL_ENV}/lib/node_modules/prettier-plugin-organize-imports/index.js`
  : "prettier-plugin-organize-imports";

/** @type {import("prettier").Options} */
module.exports = {
  plugins: [pluginOrganizeImports],
  printWidth: 96,
  overrides: [
    {
      files: "*.md",
      options: { proseWrap: "always" },
    },
    {
      files: "ui/webapp/app/**/*.tsx",
      options: {
        plugins: [pluginOrganizeImports, pluginTailwindcss],
        tailwindConfig: "ui/webapp/tailwind.config.ts",
      },
    },
  ],
};
