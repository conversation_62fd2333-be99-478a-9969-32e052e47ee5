#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create 4K YouTube videos from audio files and image files using ffmpeg.
The output videos will have the image as a static background with the audio playing.

Supports:
- Processing multiple audio files from a directory
- Using same image for all videos or matching images from an image directory
- Automatic output naming based on audio filenames
"""

import argparse
import os
import subprocess
import sys
from pathlib import Path
from typing import Optional

def create_video(image_path: str, audio_path: str, output_path: str) -> None:
    """
    Create a 4K video from an image and audio file using ffmpeg.
    
    Args:
        image_path: Path to the image file
        audio_path: Path to the audio file
        output_path: Path where the output video will be saved
    """
    if not os.path.exists(image_path):
        print(f"Error: Image file not found: {image_path}")
        sys.exit(1)
    
    if not os.path.exists(audio_path):
        print(f"Error: Audio file not found: {audio_path}")
        sys.exit(1)
        
    # Get audio duration using ffprobe
    duration_cmd = [
        'ffprobe', 
        '-v', 'error',
        '-show_entries', 'format=duration',
        '-of', 'default=noprint_wrappers=1:nokey=1',
        audio_path
    ]
    
    try:
        duration = float(subprocess.check_output(duration_cmd).decode().strip())
    except subprocess.CalledProcessError as e:
        print(f"Error getting audio duration: {e}")
        sys.exit(1)
    
    # FFmpeg command to create 4K video
    # -loop 1: Loop the image
    # -framerate 24: Set framerate to 24 fps (standard for YouTube)
    # -i {image_path}: Input image file
    # -i {audio_path}: Input audio file
    # -c:v libx264: Use H.264 video codec
    # -preset veryslow: Highest quality encoding
    # -crf 18: High quality (range 0-51, lower is better)
    # -c:a aac: Use AAC audio codec
    # -b:a 320k: High quality audio bitrate
    # -shortest: Match video duration to audio length
    cmd = [
        'ffmpeg',
        '-loop', '1',
        '-framerate', '24',
        '-i', image_path,
        '-i', audio_path,
        '-c:v', 'libx264',
        '-preset', 'veryslow',
        '-crf', '18',
        '-c:a', 'aac',
        '-b:a', '320k',
        '-shortest',
        '-pix_fmt', 'yuv420p',  # Required for compatibility
        '-vf', 'scale=3840:2160',  # 4K resolution (3840x2160)
        '-y',  # Overwrite output file if it exists
        output_path
    ]
    
    try:
        print("Creating video... This may take a while depending on the audio length.")
        subprocess.run(cmd, check=True)
        print(f"\nVideo successfully created: {output_path}")
        print(f"Duration: {duration:.2f} seconds")
        
    except subprocess.CalledProcessError as e:
        print(f"Error creating video: {e}")
        sys.exit(1)

def process_audio_folder(audio_folder: str, image_path: str, output_folder: str, image_folder: Optional[str] = None) -> None:
    """
    Process all audio files in a folder to create YouTube videos.
    
    Args:
        audio_folder: Path to folder containing audio files
        image_path: Path to default image file (used if image_folder is None)
        output_folder: Path where output videos will be saved
        image_folder: Optional path to folder containing matching images
    """
    # Create output folder if it doesn't exist
    os.makedirs(output_folder, exist_ok=True)
    
    # Get list of supported audio files
    audio_extensions = {'.mp3', '.wav', '.flac', '.m4a', '.ogg', '.aac'}
    audio_files = [f for f in os.listdir(audio_folder) 
                  if os.path.splitext(f)[1].lower() in audio_extensions]
    
    if not audio_files:
        print(f"No supported audio files found in {audio_folder}")
        sys.exit(1)
    
    # Process each audio file
    for audio_file in audio_files:
        audio_path = os.path.join(audio_folder, audio_file)
        base_name = os.path.splitext(audio_file)[0]
        
        # Determine which image to use
        current_image = image_path
        if image_folder:
            # Look for matching image with common image extensions
            image_extensions = {'.jpg', '.jpeg', '.png', '.bmp'}
            for ext in image_extensions:
                potential_image = os.path.join(image_folder, base_name + ext)
                if os.path.exists(potential_image):
                    current_image = potential_image
                    break
        
        output_path = os.path.join(output_folder, f"{base_name}.mp4")
        
        print(f"\nProcessing: {audio_file}")
        print(f"Using image: {os.path.basename(current_image)}")
        create_video(current_image, audio_path, output_path)

def main():
    parser = argparse.ArgumentParser(
        description='Create 4K YouTube videos from audio files with static images.'
    )
    parser.add_argument(
        '--audio-folder', 
        required=True,
        help='Path to folder containing audio files'
    )
    parser.add_argument(
        '--image', 
        required=True,
        help='Path to default image file to use for all videos'
    )
    parser.add_argument(
        '--output-folder',
        default='youtube_videos',
        help='Path to folder where videos will be saved (default: youtube_videos)'
    )
    parser.add_argument(
        '--image-folder',
        help='Optional: Path to folder containing images matching audio filenames'
    )
    
    args = parser.parse_args()
    process_audio_folder(args.audio_folder, args.image, args.output_folder, args.image_folder)

if __name__ == '__main__':
    main()
