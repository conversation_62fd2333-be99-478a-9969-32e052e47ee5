# macOS
.DS_Store

.idea
.venv

tmp/
cline_docs/currentTask.md

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Next.js
.next/
out/
next-env.d.ts

# TypeScript
*.tsbuildinfo
tsconfig.tsbuildinfo

# ESLint
.eslintcache

# Build outputs
dist/
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
*.swp
*.swo

# Logs
logs
*.log

# Terraform
**/.terraform/*
*.tfstate
*.tfstate.*
crash.log
crash.*.log
*.tfvars
*.tfvars.json
override.tf
override.tf.json
*_override.tf
*_override.tf.json
.terraformrc
terraform.rc
.terraform.lock.hcl
lambda.zip
