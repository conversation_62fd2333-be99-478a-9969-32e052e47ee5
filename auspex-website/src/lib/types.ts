
export interface Track {
  id: string;
  title: string;
  youtubeVideoId?: string;
}

export interface Release {
  id:string;
  title: string;
  artist: string;
  coverUrl: string;
  releaseDate: string; // YYYY-MM-DD
  youtubeLink?: string;
  tracks: Track[];
  platforms?: {
    bandcamp?: string;
    soundcloud?: string;
    spotify?: string;
    appleMusicUrl?: string;
    youtubeMusicUrl?: string;
    amazonMusicUrl?: string;
  };
}

export interface LiveSet {
  id: string;
  title: string;
  artist: string;
  youtubeVideoId: string;
  date: string; // YYYY-MM-DD
  description: string;
}
