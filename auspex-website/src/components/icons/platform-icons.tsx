
import React from 'react';

const iconProps = {
  className: "h-6 w-6 transition-opacity hover:opacity-80",
  "aria-hidden": "true" as const,
  focusable: false,
};

export const BandcampIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg {...iconProps} {...props} viewBox="0 0 24 24">
    <path d="M0 18.75l7.437-13.5H24l-7.438 13.5H0z" fill="#629aa9" />
  </svg>
);

export const SoundcloudIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg {...iconProps} {...props} viewBox="0 0 24 24" fill="#ff5500">
    <path d="M4.5 12.5c0-3.31 2.69-6 6-6 2.45 0 4.53 1.47 5.47 3.55.17-.03.33-.05.5-.05 2.21 0 4 1.79 4 4s-1.79 4-4 4H4.5c-1.38 0-2.5-1.12-2.5-2.5S3.12 12.5 4.5 12.5z" />
    <path d="M15 11h1.5v6H15z" />
    <path d="M17.5 11H19v6h-1.5z" />
    <path d="M20 11h1.5v6H20z" />
  </svg>
);

export const SpotifyIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg {...iconProps} {...props} viewBox="0 0 24 24">
    <circle cx="12" cy="12" r="12" fill="#1DB954" />
    <path d="M17.4,14.6c-1.6-1-4-0.9-6.7,0.6c-0.4,0.2-0.9,0.1-1.1-0.4c-0.2-0.4,0.1-0.9,0.4-1.1 c3.1-1.6,5.8-1.6,7.8,0.3c0.4,0.4,0.6,0.9,0.2,1.3C17.7,14.9,17.5,14.7,17.4,14.6z" fill="#000" />
    <path d="M18.1,12c-2-1.2-5-1.5-8.2,0.3c-0.5,0.2-1.1,0-1.3-0.4c-0.3-0.5-0.1-1.1,0.4-1.3c3.7-1.9,7.1-1.6,9.5,0.5 c0.5,0.4,0.7,1,0.3,1.4C18.4,12.2,18.3,12.1,18.1,12z" fill="#000" />
    <path d="M18.3,9.4C16,8,12.2,7.9,8.6,9.5c-0.6,0.2-1.2-0.1-1.4-0.6C7,8.3,7.3,7.7,7.9,7.5 c4.3-1.9,8.5-1.8,11.2,0.3c0.6,0.3,0.8,0.9,0.5,1.5C19.3,9.8,18.7,10,18.3,9.4z" fill="#000" />
  </svg>
);

export const AppleMusicIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg {...iconProps} {...props} viewBox="0 0 24 24">
    <path d="M18.318 12.382c0-2.31 1.948-3.433 2.13-3.562-.122-.184-1.077-1.584-2.67-1.63-1.51-.06-2.923.89-3.693.89-.78 0-1.896-.88-3.18-.85-1.56.03-2.953.94-3.722 2.373-1.57 2.924-.407 7.233 1.137 9.584.757 1.15 1.63 2.45 2.822 2.42 1.15-.03 1.54-0.75 2.992-0.75s1.78.75 3.022.72c1.27-.03 2.01-1.09 2.73-2.19.85-1.28 1.18-2.58 1.21-2.64-.03-.01-2.31-.85-2.31-3.663zM15.17 6.132c.69-.87 1.13-2.07 1.01-3.23-.97.06-2.13.68-2.82 1.54-.6.75-1.15 2.01-1.03 3.17.97.04 2.15-.6 2.84-1.48z" fill="#fa233b" />
  </svg>
);

export const YoutubeMusicIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg {...iconProps} {...props} viewBox="0 0 24 24">
    <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 18a6 6 0 110-12 6 6 0 010 12zm0-9a3 3 0 100 6 3 3 0 000-6z" fill="#FF0000" />
    <path d="M15.5 12L10 9v6z" fill="#FFFFFF" />
  </svg>
);

export const AmazonMusicIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg {...iconProps} {...props} viewBox="0 0 24 24" fill="#00a8e1">
    <path d="M12 0c-6.63 0-12 5.37-12 12s5.37 12 12 12 12-5.37 12-12-5.37-12-12-12zm4.24 15.35c-1.5 1.05-3.3.15-3.3-.15s.26-2.51.26-2.51.15-1.2-1.2-1.2-1.2 1.2-1.2 1.2.26 2.36.26 2.51-.83 1.35-3.3.15c-2.47-1.2-.45-4.2.15-4.65s3-1.65 3-1.65.9-1.05 2.1-1.05 2.1 1.05 2.1 1.05.75.45 3 1.65c2.55 1.2 1.65 3.45.15 4.65z" />
  </svg>
);
    
