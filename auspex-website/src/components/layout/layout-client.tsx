
"use client";

import { usePathname } from 'next/navigation';
import Header from '@/components/layout/header';
import Footer from '@/components/layout/footer';

export default function LayoutClient({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const pathname = usePathname();
  const isHomePage = pathname === '/';

  return (
    <>
      <div className="aurora-bg" />
      <div className="relative flex min-h-screen w-full flex-col">
        {!isHomePage && <Header />}
        <main className="flex-1">{children}</main>
        {!isHomePage && <Footer />}
      </div>
    </>
  );
}
