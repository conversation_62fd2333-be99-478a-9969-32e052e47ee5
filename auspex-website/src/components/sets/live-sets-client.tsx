
'use client';

import SetCard from '@/components/sets/set-card';
import { motion } from 'framer-motion';
import type { LiveSet } from '@/lib/types';

export default function LiveSetsClient({ sets }: { sets: LiveSet[] }) {
  return (
    <div className="container mx-auto px-4 py-8 pt-28">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h1 className="text-4xl lg:text-5xl font-headline mb-8 text-center text-primary">Live Sets</h1>
        <p className="text-center text-lg text-foreground/70 max-w-2xl mx-auto mb-12">
          Experience the energy of Auspex Records artists, live from festivals and sessions around the world.
        </p>
      </motion.div>
      <motion.div 
        className="grid grid-cols-1 gap-12"
        initial="offscreen"
        whileInView="onscreen"
        viewport={{ once: true, amount: 0.1 }}
        transition={{ staggerChildren: 0.3 }}
      >
        {sets.map((set) => (
          <SetCard key={set.id} set={set} />
        ))}
      </motion.div>
    </div>
  );
}
