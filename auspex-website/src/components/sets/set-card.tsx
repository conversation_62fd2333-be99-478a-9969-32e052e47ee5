
"use client";

import type { LiveSet } from '@/lib/types';
import { Card, CardContent } from '@/components/ui/card';
import { motion } from 'framer-motion';
import { YouTubeEmbed } from '../common/youtube-embed';

interface SetCardProps {
  set: LiveSet;
}

const cardVariants = {
  offscreen: {
    y: 50,
    opacity: 0
  },
  onscreen: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      bounce: 0.4,
      duration: 0.8
    }
  }
};

export default function SetCard({ set }: SetCardProps) {
  const { title, artist, youtubeVideoId, date, description } = set;
  const youtubeUrl = `https://www.youtube.com/embed/${youtubeVideoId}`;

  return (
    <motion.div variants={cardVariants}>
      <Card className="flex flex-col bg-card/50 backdrop-blur-sm transition-all duration-300 hover:shadow-primary/20 hover:shadow-lg hover:border-primary/30 h-full overflow-hidden">
        <div className="p-4 aspect-video relative group">
          <YouTubeEmbed url={youtubeUrl} />
        </div>
        <CardContent className="flex flex-col flex-grow p-4 pt-0">
          <div className="text-sm text-muted-foreground mb-2">{new Date(date).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric', timeZone: 'UTC' })}</div>
          <h3 className="font-headline text-2xl text-primary mb-1">{title}</h3>
          <h4 className="font-headline text-lg text-foreground/80 mb-4">{artist}</h4>
          <p className="text-foreground/80 flex-grow">{description}</p>
        </CardContent>
      </Card>
    </motion.div>
  );
}
