
"use client";

import type { Release } from "@/lib/types";
import Image from 'next/image';
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/card/card";

interface ReleaseCardProps {
  release: Release;
  onClick: () => void;
  isSelected: boolean;
}

const cardVariants = {
  offscreen: {
    y: 50,
    opacity: 0
  },
  onscreen: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      bounce: 0.4,
      duration: 0.8
    }
  }
};

export default function ReleaseCard({ release, onClick }: ReleaseCardProps) {
  const { title, artist, coverUrl } = release;

  return (
    <motion.div
      variants={cardVariants}
      className="cursor-pointer h-full"
      onClick={onClick}
      layoutId={`release-card-container-${release.id}`}
    >
      <Card className="h-full flex flex-col bg-card/80 shadow-2xl shadow-primary/[0.03] backdrop-blur-sm transition-all duration-300 hover:border-primary/50 overflow-hidden group">
        <div className="relative w-full aspect-square overflow-hidden">
          <motion.div layoutId={`release-image-${release.id}`} className="w-full h-full">
            <Image
              priority
              src={coverUrl}
              alt={`Cover art for ${title} by ${artist}`}
              fill
              className="object-contain transition-transform duration-500 group-hover:scale-110 p-2"
              data-ai-hint="album cover"
            />
          </motion.div>
        </div>
        <CardContent className="p-4 flex flex-col">
           <motion.h2 layoutId={`release-title-${release.id}`} className="text-xl font-headline text-primary mb-1">{title}</motion.h2>
           <motion.h3 layoutId={`release-artist-${release.id}`} className="text-md font-headline text-foreground/80 mt-auto">{artist}</motion.h3>
        </CardContent>
      </Card>
    </motion.div>
  );
}
