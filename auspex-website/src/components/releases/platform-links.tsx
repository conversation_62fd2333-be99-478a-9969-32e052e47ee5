
"use client";

import type { Release } from "@/lib/types";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>ltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  BandcampIcon,
  SoundcloudIcon,
  SpotifyIcon,
  AppleMusicIcon,
  YoutubeMusicIcon,
  AmazonMusicIcon,
} from "../icons/platform-icons";

interface PlatformLinksProps {
  platforms: NonNullable<Release["platforms"]>;
}

const platformConfig = {
  bandcamp: { icon: BandcampIcon, name: "Bandcamp" },
  soundcloud: { icon: SoundcloudIcon, name: "SoundCloud" },
  spotify: { icon: SpotifyIcon, name: "Spotify" },
  appleMusicUrl: { icon: AppleMusicIcon, name: "Apple Music" },
  youtubeMusicUrl: { icon: YoutubeMusicIcon, name: "YouTube Music" },
  amazonMusicUrl: { icon: AmazonMusicIcon, name: "Amazon Music" },
};

export default function PlatformLinks({ platforms }: PlatformLinksProps) {
  return (
    <TooltipProvider>
      <div className="flex flex-wrap gap-4">
        {Object.entries(platforms).map(([key, url]) => {
          const config = platformConfig[key as keyof typeof platformConfig];
          if (!config || !url) return null;
          const Icon = config.icon;
          return (
            <Tooltip key={key}>
              <TooltipTrigger asChild>
                <a href={url} target="_blank" rel="noopener noreferrer" className="text-white/70 hover:text-primary transition-colors transform hover:scale-110">
                  <Icon className="h-8 w-8" />
                </a>
              </TooltipTrigger>
              <TooltipContent side="bottom" className="bg-black/50 border-white/10 text-white">
                <p>{config.name}</p>
              </TooltipContent>
            </Tooltip>
          );
        })}
      </div>
    </TooltipProvider>
  );
}
