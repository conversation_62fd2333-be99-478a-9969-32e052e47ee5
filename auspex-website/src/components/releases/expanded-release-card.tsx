
"use client";

import type { Release } from "@/lib/types";
import { motion } from "framer-motion";
import { X, Download, Music, Youtube } from "lucide-react";
import { Button } from "../ui/button";
import PlatformLinks from "./platform-links";
import { DownloadOptionsDialog } from "./download-options-dialog";
import { cn } from "@/lib/utils";
import Image from "next/image";

interface ExpandedReleaseCardProps {
  release: Release;
  onClose: () => void;
  playingVideoId: string | null;
  onTrackSelect: (videoId: string | null) => void;
}

export default function ExpandedReleaseCard({ release, onClose, playingVideoId, onTrackSelect }: ExpandedReleaseCardProps) {
  
  if (!release) return null;

  const handleTrackClick = (videoId?: string) => {
    if (videoId) {
      onTrackSelect(videoId);
    }
  };

  return (
    <>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.5 }}
        className="fixed inset-0 z-40 bg-black/80 backdrop-blur-sm"
        onClick={onClose}
      />
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 pointer-events-none">
        <motion.div
          layoutId={`release-card-container-${release.id}`}
          className="w-full max-w-4xl max-h-[90vh] grid grid-cols-1 md:grid-cols-2 bg-card/80 rounded-xl overflow-hidden shadow-2xl shadow-primary/20 pointer-events-auto"
        >
          <motion.div className="relative h-full w-full hidden md:block min-h-[300px]">
             <Image
                src={release.coverUrl}
                alt={release.title}
                fill
                className="object-contain"
                priority
                data-ai-hint="album cover"
              />
             <div className="absolute inset-0 bg-gradient-to-b from-black/20 via-transparent to-black/80 z-20 pointer-events-none" />
          </motion.div>
          <div className="flex flex-col h-full max-h-[90vh]">
            <div className="p-6 flex-shrink-0 flex justify-between items-start gap-4">
              <div>
                <motion.h2 layoutId={`release-title-${release.id}`} className="text-3xl font-headline text-primary mb-1">{release.title}</motion.h2>
                <motion.h3 layoutId={`release-artist-${release.id}`} className="text-xl font-headline text-foreground/80">{release.artist}</motion.h3>
              </div>
            </div>

            <div className="p-6 pt-0 flex-grow overflow-y-auto flex flex-col">
              <div className="mb-6 flex-grow">
                <h4 className="font-headline text-lg mb-3 text-foreground/80">Tracklist</h4>
                <ol className="space-y-1 text-foreground/90">
                  {release.tracks.map((track) => (
                    <li key={track.id}>
                      <button
                        onClick={() => handleTrackClick(track.youtubeVideoId)}
                        disabled={!track.youtubeVideoId}
                        className={cn(
                          "w-full text-left p-2 rounded-md transition-colors flex items-center gap-3",
                          "disabled:opacity-50 disabled:cursor-not-allowed",
                          track.youtubeVideoId && "hover:bg-primary/10",
                          playingVideoId === track.youtubeVideoId && "bg-primary/20 text-primary"
                        )}
                      >
                        <Music size={16} className="flex-shrink-0" />
                        <span>{track.title}</span>
                        {playingVideoId === track.youtubeVideoId && (
                          <Youtube size={16} className="ml-auto text-primary" />
                        )}
                      </button>
                    </li>
                  ))}
                </ol>
              </div>

              {release.platforms && (
                <div className="mb-6">
                  <h4 className="font-headline text-lg mb-3 text-foreground/80">Listen On</h4>
                  <PlatformLinks platforms={release.platforms} />
                </div>
              )}

              <div className="mt-auto pt-4">
                <DownloadOptionsDialog release={release}>
                  <Button size="lg" variant="outline" className="w-full font-bold text-lg bg-primary/5 border-primary/20 text-primary hover:bg-primary/10">
                    <Download className="mr-2 h-5 w-5" />
                    Download
                  </Button>
                </DownloadOptionsDialog>
              </div>
            </div>
          </div>
        </motion.div>
        <motion.div
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0, opacity: 0 }}
          transition={{ delay: 0.2 }}
          className="absolute top-4 right-4 pointer-events-auto"
        >
          <Button size="icon" variant="ghost" onClick={onClose} className="bg-background/50 hover:bg-background/80 rounded-full h-12 w-12 text-white">
            <X size={24} />
          </Button>
        </motion.div>
      </div>
    </>
  );
}
