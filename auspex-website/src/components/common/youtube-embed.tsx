
"use client";

import React, { useEffect, useState } from 'react';

interface YouTubeEmbedProps {
  url: string;
  autoplay?: boolean;
}

export function YouTubeEmbed({ url, autoplay = false }: YouTubeEmbedProps) {
  const [embedUrl, setEmbedUrl] = useState<string | null>(null);

  useEffect(() => {
    if (!url) {
      setEmbedUrl(null);
      return;
    }
    
    const urlWithParams = new URL(url);
    if (autoplay) {
      urlWithParams.searchParams.set('autoplay', '1');
    }
    urlWithParams.searchParams.set('controls', '1');
    urlWithParams.searchParams.set('enablejsapi', '1');
    urlWithParams.searchParams.set('modestbranding', '1');
    urlWithParams.searchParams.set('rel', '0');
    urlWithParams.searchParams.set('playsinline', '1');
    
    // Set origin for security, which can only be done on the client
    if (typeof window !== 'undefined') {
      urlWithParams.searchParams.set('origin', window.location.origin);
    }
    
    setEmbedUrl(urlWithParams.toString());
  }, [url, autoplay]);

  if (!embedUrl) {
    // You can render a loading state or null
    return <div className="w-full h-full bg-black" />;
  }

  return (
    <div className="w-full h-full">
      <iframe
        src={embedUrl}
        title="YouTube video player"
        frameBorder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        allowFullScreen
        className="w-full h-full"
      />
    </div>
  );
}

export default YouTubeEmbed;
