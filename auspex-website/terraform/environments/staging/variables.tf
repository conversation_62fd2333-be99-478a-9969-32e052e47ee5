variable "aws_region" {
  description = "AWS region for all resources"
  type        = string
  default     = "us-west-1"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "staging"
}

variable "domain_name" {
  description = "Base domain name for the website"
  type        = string
  default     = "auspexrecords.com"
}

variable "website_domain" {
  description = "Full domain name for the website"
  type        = string
  default     = "stage.auspexrecords.com"
}

variable "website_bucket_name" {
  description = "Name of the S3 bucket for website hosting"
  type        = string
  default     = "auspex-records-website-staging"
}



variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default = {
    Project     = "Auspex Records Website"
    Environment = "Staging"
    Terraform   = "true"
  }
}
