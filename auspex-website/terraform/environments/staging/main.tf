# Website resources
module "website" {
  source = "../../modules/website"

  website_bucket_name = var.website_bucket_name
  website_domain      = var.website_domain
  acm_certificate_arn = module.domains.acm_certificate_arn
  tags                = var.tags
}

# Domain resources
module "domains" {
  source = "../../modules/domains"
  providers = {
    aws.us-east-1 = aws.us-east-1
  }

  domain_name              = var.domain_name
  website_domain           = var.website_domain
  cloudfront_domain_name   = module.website.cloudfront_domain
  cloudfront_hosted_zone_id = module.website.cloudfront_hosted_zone_id
  tags                     = var.tags
}
