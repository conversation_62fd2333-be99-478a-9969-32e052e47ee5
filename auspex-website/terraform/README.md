# Auspex Records Website Terraform Configuration

This directory contains the Terraform configuration for the Auspex Records website infrastructure, organized with separate staging and production environments using a modular approach.

## Architecture Overview

The infrastructure uses a **multi-environment, modular architecture** with:
- **Separate state files** for staging and production
- **Shared modules** for consistent resource definitions
- **Environment-specific configurations** for different domains and settings
- **Automated state management** with S3 backend and DynamoDB locking

## Directory Structure

```
terraform/
├── environments/           # Environment-specific configurations
│   ├── staging/           # Staging environment (stage.auspexrecords.com)
│   │   ├── provider.tf    # Provider and backend configuration
│   │   ├── variables.tf   # Staging-specific variables
│   │   ├── main.tf        # Main infrastructure configuration
│   │   └── outputs.tf     # Output definitions
│   └── prod/              # Production environment (auspexrecords.com)
│       ├── providers.tf   # Provider and backend configuration
│       ├── variables.tf   # Production-specific variables
│       ├── app.tf         # Main infrastructure configuration
│       └── outputs.tf     # Output definitions
├── modules/               # Reusable Terraform modules
│   ├── website/          # S3 bucket and CloudFront distribution
│   ├── api/              # API Gateway and Lambda function
│   ├── database/         # DynamoDB tables
│   └── domains/          # Route53 DNS and ACM certificates
├── bootstrap/            # Bootstrap resources for Terraform state
│   └── main.tf           # S3 bucket and DynamoDB table for state
└── seed-data.sh          # Script to seed data into DynamoDB
```

## Modules

### Website Module

The website module creates the S3 bucket and CloudFront distribution for hosting the static website.

### API Module

The API module creates the API Gateway and Lambda function for the serverless API.

### Database Module

The database module creates the DynamoDB tables for storing data.

### Domains Module

The domains module creates the Route53 records and ACM certificates for the website domain.

## Environments

### Staging

The staging environment is deployed to `stage.auspexrecords.com`.

### Production

The production environment is deployed to `auspexrecords.com`.

## Usage

### Bootstrap (One-time setup)

Before deploying any environment, create the shared state management resources:

```bash
cd terraform/bootstrap
terraform init
terraform apply
```

### Deploy Staging Environment

```bash
cd terraform/environments/staging
terraform init
terraform plan    # Review the changes
terraform apply   # Deploy the infrastructure
```

### Deploy Production Environment

```bash
cd terraform/environments/prod
terraform init
terraform plan    # Review the changes
terraform apply   # Deploy the infrastructure
```

### Seed Database

```bash
cd terraform
./seed-data.sh staging  # Seed staging environment
./seed-data.sh prod     # Seed production environment
```

### Deploy Frontend

Use the automated deployment scripts from the project root:

```bash
# Deploy to staging
npm run deploy:staging

# Deploy to production
npm run deploy:prod
```

### Manual Frontend Deployment (if needed)

```bash
# After building the frontend
cd terraform/environments/staging
aws s3 sync ../../../dist/ s3://$(terraform output -raw s3_bucket_name)
```

## State Management

Terraform state is stored in an S3 bucket with DynamoDB locking for consistency and safety. Each environment maintains its own isolated state file:

| Environment | State File Location | Purpose |
|-------------|-------------------|---------|
| **Staging** | `s3://auspex-terraform-state/staging/terraform.tfstate` | Staging infrastructure state |
| **Production** | `s3://auspex-terraform-state/prod/terraform.tfstate` | Production infrastructure state |

### State Isolation Benefits

- **Independent deployments** - Changes to staging don't affect production
- **Separate resource management** - Each environment has its own AWS resources
- **Reduced risk** - Accidental changes are contained to one environment
- **Parallel development** - Multiple developers can work on different environments

### Bootstrap Resources

The bootstrap configuration creates the shared state management infrastructure:

- **S3 Bucket**: `auspex-terraform-state` (with versioning enabled)
- **DynamoDB Table**: `terraform-state-lock` (for state locking)
- **Encryption**: Server-side encryption enabled on S3 bucket

This bootstrap step only needs to be run once before any environment deployment.
